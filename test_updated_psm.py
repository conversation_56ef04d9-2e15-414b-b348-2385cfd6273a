#!/usr/bin/env python3
"""
测试更新后的PSM匹配函数
"""
import sys
import os
sys.path.append('src_new_new')

import pandas as pd
import numpy as np
import logging
import time
from datetime import datetime

# 导入更新后的函数
from src_new_new.compile_control_group_psm_knn import compile_control_group_psm_knn

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_updated_psm():
    """测试更新后的PSM算法"""
    print("="*60)
    print("TESTING UPDATED PSM ALGORITHM")
    print("="*60)
    
    start_time = time.time()
    
    # 读取测试数据
    test_file = 'result/20250730_did_result/productivity_with_propensity_scores_with_attritions_365_test_sample.csv'
    print(f"Loading test data from: {test_file}")
    
    if not os.path.exists(test_file):
        print(f"Error: Test file not found: {test_file}")
        return
    
    p_test = pd.read_csv(test_file)
    print(f"Test data shape: {p_test.shape}")
    
    # 数据预处理 - 完全按照原始逻辑
    p_test_attrition = p_test[p_test['someone_left'] == 1].copy()
    p_test_attrition = p_test_attrition[p_test_attrition['feature_sigmod_add'].notnull()]
    p_test_attrition = p_test_attrition[p_test_attrition['feature_sigmod_add'] != 0.5]
    
    print(f"Attrition data shape after filtering: {p_test_attrition.shape}")
    
    p_test = p_test.fillna(0)
    p_test_attrition = p_test_attrition.fillna(0)
    
    # 转换类型
    if 'burst' in p_test_attrition.columns:
        p_test_attrition['burst'] = p_test_attrition['burst'].astype(int)
    
    # 调用更新后的PSM函数进行匹配
    print("Starting PSM matching...")
    try:
        # 直接导入函数而不是从模块导入
        from src_new_new import compile_control_group_psm_knn
        
        matched_pairs, treatment_features_df, control_features_df = compile_control_group_psm_knn(
            p_test_attrition,
            p_test['repo_name'].tolist(),
            p_test,
            n_neighbors=5,
            timewindow_weeks=12,
            feature_columns=['feature_sigmod_add'],
            extra_candidates=10,
            batch_size=1000
        )
    except ImportError as e:
        print(f"Import error: {e}")
        print("Trying alternative import method...")
        
        # 直接从文件中导入函数
        import importlib.util
        spec = importlib.util.spec_from_file_location(
            "psm_module", 
            "src_new_new/20250629_04_PSM_matching_multi_limits_improved.py"
        )
        psm_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(psm_module)
        
        matched_pairs, treatment_features_df, control_features_df = psm_module.compile_control_group_psm_knn(
            p_test_attrition,
            p_test['repo_name'].tolist(),
            p_test,
            n_neighbors=5,
            timewindow_weeks=12,
            feature_columns=['feature_sigmod_add'],
            extra_candidates=10,
            batch_size=1000
        )
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"\n=== UPDATED PSM RESULTS ===")
    print(f"Processing time: {processing_time:.2f} seconds")
    print(f"Total matched pairs: {len(matched_pairs)}")
    print(f"Treatment features shape: {treatment_features_df.shape}")
    print(f"Control features shape: {control_features_df.shape}")
    
    # 显示一些详细信息
    if matched_pairs:
        first_key = list(matched_pairs.keys())[0]
        first_match = matched_pairs[first_key]
        print(f"\nSample match (burst {first_key}):")
        print(f"  Repo: {first_match['repo_name']}")
        print(f"  Treatment time: {first_match['treatment_time']}")
        print(f"  Number of controls: {len(first_match['controls'])}")
        print(f"  Control repos: {[c['repo_name'] for c in first_match['controls']]}")
    
    return matched_pairs, processing_time

if __name__ == "__main__":
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    try:
        test_updated_psm()
        print("✅ Test completed successfully!")
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
    print(f"Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
